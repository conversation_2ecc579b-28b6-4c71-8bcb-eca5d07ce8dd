import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

// Force dynamic rendering to prevent static generation errors when using headers()
// This is required because getServerSession() uses headers() internally
export const dynamic = "force-dynamic";

// Mock notification settings data - realistic data following established schema
// TODO: Replace with actual backend API call when backend is ready
const MOCK_NOTIFICATION_SETTINGS: INotificationSetting[] = [
  {
    id: "1",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "document_reminder",
    channels: ["email", "in_app"],
    isEnabled: true,
    customSchedule: 24,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "2",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "application_submitted",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "3",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "agent_assigned",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "4",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "status_update",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "5",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "agent_query",
    channels: ["email", "in_app"],
    isEnabled: false,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "6",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "document_rejected",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "7",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "authority_query",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "8",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "deadline_warning",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    customSchedule: 48,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "9",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "missing_document",
    channels: ["email", "in_app"],
    isEnabled: true,
    customSchedule: 72,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "10",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "eligibility_confirmation",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "11",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "payment_confirmation",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "12",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "final_decision",
    channels: ["email", "sms", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "13",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "system_maintenance",
    channels: ["email", "in_app"],
    isEnabled: false,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "14",
    userId: "admin-user-1",
    userType: "admin",
    notificationType: "escalation_notice",
    channels: ["email", "in_app"],
    isEnabled: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
];

/**
 * GET /api/notifications/settings
 *
 * Retrieves notification settings for the authenticated user.
 * Currently uses mock data to prevent 404 errors from undeveloped backend endpoints.
 * Supports optional userType query parameter for filtering.
 *
 * @param {NextRequest} request Next.js request object containing optional query parameters
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to access notification settings.",
        },
        { status: 401 }
      );
    }

    // Extract optional userType parameter for filtering
    const { searchParams } = new URL(request.url);
    const userType = searchParams.get("userType");

    // Simulate realistic API delay (300ms) to mimic backend behavior
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Filter by userType if provided (for future compatibility)
    let filteredSettings = MOCK_NOTIFICATION_SETTINGS;
    if (userType) {
      filteredSettings = MOCK_NOTIFICATION_SETTINGS.filter(
        (setting) => setting.userType === userType
      );
    }

    // Return mock data in the same format as the real API would
    return NextResponse.json({
      success: true,
      data: filteredSettings,
      message: "Notification settings retrieved successfully",
    });
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in GET /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while retrieving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications/settings
 *
 * Updates notification settings for the authenticated user.
 * Currently uses mock implementation to prevent 404 errors from undeveloped backend endpoints.
 * Accepts an array of notification settings in the request body.
 *
 * @param {NextRequest} request Next.js request object containing settings data
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to update notification settings.",
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();

    // Validate that settings array is provided
    if (!body.settings || !Array.isArray(body.settings)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid request format. Settings array is required.",
        },
        { status: 400 }
      );
    }

    // Simulate realistic API delay (400ms) to mimic backend behavior
    await new Promise((resolve) => setTimeout(resolve, 400));

    // Mock successful update - in real implementation, this would save to database
    // For now, we just validate the data structure and return success
    const updatedSettings = body.settings.map(
      (setting: any, index: number) => ({
        ...setting,
        id: setting.id || `mock-${index + 1}`,
        userId: session.user?.id || "current-user",
        updated_at: new Date().toISOString(),
      })
    );

    // Mock successful update - settings would be saved to database in real implementation

    return NextResponse.json({
      success: true,
      data: updatedSettings,
      message: "Notification settings updated successfully",
    });
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in PUT /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while saving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}
