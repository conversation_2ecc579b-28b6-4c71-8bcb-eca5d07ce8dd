import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

// Force dynamic rendering to prevent static generation errors when using headers()
// This is required because getServerSession() uses headers() internally
export const dynamic = "force-dynamic";

/**
 * GET /api/notifications/settings
 *
 * Retrieves notification settings for the authenticated user from the backend API.
 * Integrates with the actual backend endpoint GET /notifications/settings.
 * Returns notification preferences in the expected format.
 *
 * @param {NextRequest} request Next.js request object containing optional query parameters
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to access notification settings.",
        },
        { status: 401 }
      );
    }

    // Call backend API to retrieve notification settings
    const backendUrl = `${apiUrl}/notifications/settings`;

    const response = await fetch(backendUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      cache: "no-store",
    });

    const data = await response.json();

    if (response.ok) {
      // Return backend data in consistent format
      return NextResponse.json({
        success: true,
        data: data,
        message: "Notification settings retrieved successfully",
      });
    } else {
      // Handle backend errors with user-friendly messages
      const errorMessage =
        data.message ||
        "Unable to retrieve notification settings. Please try again later.";
      return NextResponse.json(
        {
          success: false,
          message: errorMessage,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in GET /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while retrieving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications/settings
 *
 * Updates notification settings for the authenticated user via backend API.
 * Integrates with the actual backend endpoint PUT /notifications/settings.
 * Accepts notification preferences in the expected format.
 *
 * @param {NextRequest} request Next.js request object containing settings data
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to update notification settings.",
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();

    // Validate request body structure
    if (!body || typeof body !== "object") {
      return NextResponse.json(
        {
          success: false,
          message:
            "Invalid request format. Notification settings data is required.",
        },
        { status: 400 }
      );
    }

    // Call backend API to update notification settings
    const backendUrl = `${apiUrl}/notifications/settings`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (response.ok) {
      // Return backend response in consistent format
      return NextResponse.json({
        success: true,
        data: data,
        message: "Notification settings updated successfully",
      });
    } else {
      // Handle backend errors with user-friendly messages
      const errorMessage =
        data.message ||
        "Unable to update notification settings. Please try again later.";
      return NextResponse.json(
        {
          success: false,
          message: errorMessage,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error in PUT /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while saving notification settings. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}
