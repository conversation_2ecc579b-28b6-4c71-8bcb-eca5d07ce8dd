import revalidateTag from "@/utils/revalidate-tag";
import { failed, success } from "@/utils/tools";
import { apiUrl } from "@/utils/urls";
import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "@/lib/axios";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export const useCreateMentor = () => {
  const { data: session } = useSession();
  const router = useRouter();
  return useMutation({
    mutationFn: async (data: IMentor) => {
      const res = await axios.post(`${apiUrl}/mentor/admin/register`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["mentors"]);
      router.push("/mentor");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateMentor = (id: string) => {
  const { data: session } = useSession();
  const router = useRouter();
  return useMutation({
    mutationFn: async (data: IMentor) => {
      const res = await axios.patch(`${apiUrl}/mentor/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["mentors"]);
      revalidateTag(["mentor", data.id]);
      router.push("/mentor");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

export const useDeleteMentor = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/mentor/admin/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["mentors"]);
      toast.success(`${data.name} removed sucessfully`, success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// ...................Service..................................

export const useCreateService = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IService) => {
      const res = await axios.post(`${apiUrl}/services/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Services Added", success);
      revalidateTag(["mentor", data.id]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateService = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IService) => {
      const res = await axios.patch(`${apiUrl}/services/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Mentor Service Updated", success);
      revalidateTag(["mentor", data.id]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useDeleteService = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/services/admin/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success(`${data.name} removed`, success);
      revalidateTag(["mentor", data.id]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// .................... Blog ......................................

export const useCreateBlog = () => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IBlog) => {
      const res = await axios.post(`${apiUrl}/blog`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Blog Added", success);
      revalidateTag(["blogs"]);
      router.push("/blog");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateBlog = (id: string) => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IBlog) => {
      const res = await axios.patch(`${apiUrl}/blog/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Blog Updated", success);
      revalidateTag(["blog", data.slug]);
      revalidateTag(["blogs"]);
      router.push("/blog");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useDeleteBlog = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/blog/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Blog removed successfully", success);
      revalidateTag(["blogs"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
// .................... Customer Review ......................................

export const useCreateCustomerReview = () => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ICustomerReview) => {
      const res = await axios.post(`${apiUrl}/customer-review`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Customer Review Added", success);
      revalidateTag(["customer-review"]);
      router.push("/customer-review");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useUpdateCustomerReview = (id: string) => {
  const router = useRouter();
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ICustomerReview) => {
      const res = await axios.patch(`${apiUrl}/customer-review/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Customer Review Updated", success);
      revalidateTag(["customer-review", data.id]);
      revalidateTag(["customer-review"]);
      router.push("/customer-review");
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useRemoveCustomerReview = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/customer-review/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Customer Review removed successfully", success);
      revalidateTag(["customer-review"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

// ....................Package.....................

export const useCreatePackage = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IPackage) => {
      const res = await axios.post(`${apiUrl}/packages`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Package Added", success);
      revalidateTag(["packages"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdatePackage = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IPackage) => {
      const res = await axios.patch(`${apiUrl}/packages/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Package updated sucessfully", success);
      revalidateTag(["packages"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useRemovePackage = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/packages/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Package removed sucessfully", success);
      revalidateTag(["packages"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// ....................Immigration.....................

export const useCreateImmigration = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IImmigration) => {
      const res = await axios.post(`${apiUrl}/immigration`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Immigration Service Added", success);
      revalidateTag(["immigration"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useUpdateImmigration = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: IImmigration) => {
      const res = await axios.patch(`${apiUrl}/immigration/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Immigration Service updated sucessfully", success);
      revalidateTag(["immigration"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useRemoveImmigration = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/immigration/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Immigration Service removed sucessfully", success);
      revalidateTag(["immigration"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

export const useToggleImmigrationVisibility = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async ({
      id,
      websiteVisible,
    }: {
      id: string;
      websiteVisible: boolean;
    }) => {
      const res = await axios.patch(
        `${apiUrl}/immigration/${id}/visibility`,
        { website_visible: websiteVisible },
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success(
        "Immigration Service visibility updated successfully",
        success
      );
      revalidateTag(["immigration"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update visibility",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};
// ....................Training.....................

export const useCreateTraining = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ITraining) => {
      const res = await axios.post(`${apiUrl}/training`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("New Training Service Added", success);
      revalidateTag(["trainings"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useUpdateTraining = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: ITraining) => {
      const res = await axios.patch(`${apiUrl}/training/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Training Service updated sucessfully", success);
      revalidateTag(["trainings"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useRemoveTraining = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/training/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Training Service removed sucessfully", success);
      revalidateTag(["trainings"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
// .................. Progress...................

export const useProgress = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: { id: string; status: string; type: string }) => {
      const { type, ...result } = data;
      const res = await axios.patch(
        `${apiUrl}/admin/user/progress/${type}`,
        result,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["user", data.userId]);
      toast.success("Sucessfully updated progress", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};
export const useGuestProgress = (validate: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: { id: string; status: string; type: string }) => {
      const { type, ...result } = data;
      const res = await axios.patch(
        `${apiUrl}/admin/user/progress/${type}`,
        result,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag([validate, data.userId]);
      toast.success("Sucessfully updated progress", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        description: "Please try after few min",
        ...failed,
      });
    },
  });
};

// ..............User...............

export const useCreateUser = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: {
      name: string;
      email: string;
      password?: string;
    }) => {
      const res = await axios.post(`${apiUrl}/user/admin`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["users"]);
      toast.success("Admin added new user sucessfully", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};
export const useUpdateUser = (id: string) => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: {
      name: string;
      email: string;
      password?: string;
    }) => {
      const res = await axios.patch(`${apiUrl}/user/admin/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      revalidateTag(["users"]);
      toast.success("Admin updated user detail sucessfully", success);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

export const useRemoveUser = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/user/admin/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("User removed sucessfully", success);
      revalidateTag(["users"]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

// ...........Comments...................................
export const useRemoveComments = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const res = await axios.delete(`${apiUrl}/comment/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return res.data;
    },
    onSuccess: (data: any) => {
      toast.success("Comment removed sucessfully", success);
      revalidateTag(["comments", data.blogId]);
    },
    onError: (error) => {
      // @ts-ignore
      toast.error(error.response.data.message, {
        ...failed,
      });
    },
  });
};

// ...........Payment Progress (v2 API)...................................
export const useUpdatePaymentProgress = () => {
  return useMutation({
    mutationFn: async (data: {
      paymentId: string;
      progress:
        | "Accepted"
        | "Rejected"
        | "Pending"
        | "Completed"
        | "Active"
        | "Inactive"
        | "Blocked"
        | "Cancelled";
    }) => {
      const res = await axios.patch(
        `/api/v2/payment/progress`,
        {
          paymentId: data.paymentId,
          progress: data.progress,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      return res.data;
    },
    onSuccess: (data: PaymentProgressUpdateResponse) => {
      toast.success("Payment progress updated successfully", success);
      revalidateTag(["payment-history"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update payment progress",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

// Payment Creation Hook (v2 API)
export const useCreatePayment = () => {
  return useMutation({
    mutationFn: async (data: CreatePaymentRequest) => {
      const response = await fetch("/api/v2/payment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create payment");
      }

      return response.json() as Promise<CreatePaymentResponse>;
    },
    onSuccess: (data: CreatePaymentResponse) => {
      if (data.stripe_link) {
        toast.success("Payment initiated successfully", {
          description: "Redirecting to Stripe checkout...",
          ...success,
        });
      } else {
        toast.success("Payment created successfully", {
          description: "Payment has been recorded",
          ...success,
        });
      }
      revalidateTag(["payment-history"]);
    },
    onError: (error: any) => {
      const errorMessage = "Failed to create payment";
      let errorDescription = "An unexpected error occurred";

      if (error.message) {
        if (
          error.message.includes("network") ||
          error.message.includes("fetch")
        ) {
          errorDescription =
            "Network error. Please check your connection and try again.";
        } else if (
          error.message.includes("validation") ||
          error.message.includes("required")
        ) {
          errorDescription = "Please check all required fields and try again.";
        } else if (error.message.includes("unauthorized")) {
          errorDescription =
            "Session expired. Please refresh the page and try again.";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        ...failed,
      });
    },
  });
};

// Document Master CRUD Hooks
export const useCreateDocumentMaster = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (
      data: Omit<IDocumentMaster, "id" | "created_at" | "updated_at">
    ) => {
      const response = await axios.post(`${apiUrl}/document-master`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Document master created successfully", {
        description: "The document master has been added",
        ...success,
      });
      revalidateTag(["document-masters", "immigration-documents"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to create document master",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useUpdateDocumentMaster = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async ({ id, ...data }: IDocumentMaster) => {
      const response = await axios.patch(
        `${apiUrl}/document-master/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Document master updated successfully", {
        description: "The document master has been updated",
        ...success,
      });
      revalidateTag(["document-masters", "immigration-documents"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update document master",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useDeleteDocumentMaster = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await axios.delete(`${apiUrl}/document-master/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Document master deleted successfully", {
        description: "The document master has been removed",
        ...success,
      });
      revalidateTag(["document-masters", "immigration-documents"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to delete document master",
        {
          description: "Please check if the document is in use",
          ...failed,
        }
      );
    },
  });
};

// Workflow Master CRUD Hooks
export const useCreateWorkflowMaster = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (
      data: Omit<IWorkflowMaster, "id" | "created_at" | "updated_at">
    ) => {
      const response = await axios.post(`${apiUrl}/workflow-master`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Workflow master created successfully", {
        description: "The workflow master has been added",
        ...success,
      });
      revalidateTag(["workflow-masters"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to create workflow master",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useUpdateWorkflowMaster = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async ({ id, ...data }: IWorkflowMaster) => {
      const response = await axios.patch(
        `${apiUrl}/workflow-master/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Workflow master updated successfully", {
        description: "The workflow master has been updated",
        ...success,
      });
      revalidateTag(["workflow-masters"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update workflow master",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useDeleteWorkflowMaster = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await axios.delete(`${apiUrl}/workflow-master/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Workflow master deleted successfully", {
        description: "The workflow master has been removed",
        ...success,
      });
      revalidateTag(["workflow-masters"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to delete workflow master",
        {
          description: "Please check if the workflow is in use",
          ...failed,
        }
      );
    },
  });
};

// Workflow Template CRUD Hooks
export const useCreateWorkflowTemplate = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (
      data: Omit<IWorkflowTemplate, "id" | "createdAt" | "updatedAt">
    ) => {
      const response = await axios.post(`${apiUrl}/workflow-templates`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Workflow template created successfully", {
        description: "The workflow template has been added",
        ...success,
      });
      revalidateTag(["workflow-templates"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to create workflow template",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useUpdateWorkflowTemplate = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async ({ id, ...data }: IWorkflowTemplate) => {
      const response = await axios.patch(
        `${apiUrl}/workflow-templates/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Workflow template updated successfully", {
        description: "The workflow template has been updated",
        ...success,
      });
      revalidateTag(["workflow-templates"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update workflow template",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useUpdateWorkflowTemplateDefault = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async ({
      id,
      isDefault,
    }: {
      id: string;
      isDefault: boolean;
    }) => {
      const response = await axios.put(
        `${apiUrl}/workflow-templates/${id}/default`,
        { isDefault },
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Default status updated successfully", {
        description: "The workflow template default status has been updated",
        ...success,
      });
      revalidateTag(["workflow-templates"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update default status",
        {
          description: "Please try again later",
          ...failed,
        }
      );
    },
  });
};

export const useDeleteWorkflowTemplate = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await axios.delete(
        `${apiUrl}/workflow-templates/${id}`,
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Workflow template deleted successfully", {
        description: "The workflow template has been removed",
        ...success,
      });
      revalidateTag(["workflow-templates"]);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to delete workflow template",
        {
          description: "Please check if the template is in use",
          ...failed,
        }
      );
    },
  });
};

// Agent CRUD Hooks
export const useCreateAgent = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (
      data: Omit<
        IAgent,
        "id" | "created_at" | "updated_at" | "created_by_admin"
      >
    ) => {
      const response = await axios.post(`${apiUrl}/agents/register`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Agent created successfully", {
        description: "The agent has been added",
        ...success,
      });
      revalidateTag(["agents"]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create agent", {
        description: "Please try again later",
        ...failed,
      });
    },
  });
};

export const useUpdateAgent = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async ({ id, ...data }: IAgent) => {
      const response = await axios.put(`${apiUrl}/agents/${id}`, data, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Agent updated successfully", {
        description: "The agent has been updated",
        ...success,
      });
      revalidateTag(["agents"]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to update agent", {
        description: "Please try again later",
        ...failed,
      });
    },
  });
};

export const useUpdateAgentPassword = () => {
  return useMutation({
    mutationFn: async (data: {
      id: string;
      currentPassword: string;
      newPassword: string;
    }) => {
      const response = await fetch(`/api/agents/${data.id}/password`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          currentPassword: data.currentPassword,
          newPassword: data.newPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Password updated successfully", {
        description: "Your password has been changed",
        ...success,
      });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update password", {
        description: "Please check your current password and try again",
        ...failed,
      });
    },
  });
};

export const useDeleteAgent = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await axios.delete(`${apiUrl}/agents/${id}`, {
        headers: {
          Authorization: `Bearer ${session?.backendTokens.accessToken}`,
        },
      });
      return response.data;
    },
    onSuccess: () => {
      toast.success("Agent deleted successfully", {
        description: "The agent has been removed",
        ...success,
      });
      revalidateTag(["agents"]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to delete agent", {
        description: "Please check if the agent is assigned to applications",
        ...failed,
      });
    },
  });
};

// Agent Assignment Hook
export const useAssignAgent = () => {
  const { data: session } = useSession();
  return useMutation({
    mutationFn: async (data: { applicationId: string; agentId: string }) => {
      const response = await axios.put(
        `${apiUrl}/applications/${data.applicationId}/assign`,
        { agentId: data.agentId },
        {
          headers: {
            Authorization: `Bearer ${session?.backendTokens.accessToken}`,
          },
        }
      );
      return response.data;
    },
    onSuccess: () => {
      toast.success("Agent assigned successfully", {
        description: "The agent has been assigned to the application",
        ...success,
      });
      revalidateTag(["applications", "agents"]);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to assign agent", {
        description: "Please try again later",
        ...failed,
      });
    },
  });
};

// Application Document Upload Hook
export const useUploadApplicationDocument = (applicationId: string) => {
  return useMutation({
    mutationFn: async (data: {
      file: File;
      document_name: string;
      stage_order: number;
      document_id?: string;
    }) => {
      const formData = new FormData();
      formData.append("file", data.file);
      formData.append("document_name", data.document_name);
      formData.append("stage_order", data.stage_order.toString());
      if (data.document_id) {
        formData.append("document_id", data.document_id);
      }

      // Use fetch instead of axios to avoid baseURL issues
      const response = await fetch(
        `/api/applications/${applicationId}/document`,
        {
          method: "POST",
          body: formData,
          // Don't set Content-Type for FormData - let the browser set it with boundary
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Document uploaded successfully", {
        description: "The document has been uploaded to the application",
        ...success,
      });
      revalidateTag(["applications", `application-${applicationId}`]);
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to upload document", {
        description: "Please try again later",
        ...failed,
      });
    },
  });
};

// Document Status Update Hook
export const useUpdateDocumentStatus = (applicationId: string) => {
  return useMutation({
    mutationFn: async (data: {
      documentId: string;
      status: DocumentStatus;
      reason?: string;
    }) => {
      const response = await fetch(
        `/api/applications/${applicationId}/documents/${data.documentId}/status`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: data.status,
            reason: data.reason,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Document status updated successfully", {
        description: "The document status has been updated",
        ...success,
      });
      revalidateTag(["applications", `application-${applicationId}`]);
    },
    onError: (error: any) => {
      // Error logging is handled by the mutation framework
      const errorMessage = error.message || "Failed to update document status";
      const isDocumentNotFound = errorMessage.includes(
        "Application document not found"
      );

      toast.error(errorMessage, {
        description: isDocumentNotFound
          ? "Document ID mismatch. Please refresh the page and try again."
          : "Please try again later",
        ...failed,
      });
    },
  });
};

// Document Request Hook
export const useRequestDocument = (applicationId: string) => {
  return useMutation({
    mutationFn: async (data: DocumentRequestBody) => {
      const response = await fetch(
        `/api/applications/${applicationId}/documents/request`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Document request submitted successfully", {
        description: "The document request has been submitted",
        ...success,
      });
      revalidateTag(["applications", `application-${applicationId}`]);
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to submit document request", {
        description: "Please try again later",
        ...failed,
      });
    },
  });
};

// Estimated Completion Hook
export const useUpdateEstimatedCompletion = (applicationId: string) => {
  return useMutation({
    mutationFn: async (data: { estimated_completion: string }) => {
      const response = await fetch(
        `/api/applications/${applicationId}/estimated-completion`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Estimated completion date updated successfully", {
        description: "The estimated completion date has been updated",
        ...success,
      });
      revalidateTag(["applications", `application-${applicationId}`]);
    },
    onError: (error: Error) => {
      toast.error("Failed to update estimated completion date", {
        description: error.message,
        ...failed,
      });
    },
  });
};

// Note Update Hook
export const useUpdateNote = (applicationId: string) => {
  return useMutation({
    mutationFn: async (data: { note: string }) => {
      const response = await fetch(`/api/applications/${applicationId}/note`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Note updated successfully", {
        description: "The note has been updated",
        ...success,
      });
      revalidateTag(["applications", `application-${applicationId}`]);
    },
    onError: (error: Error) => {
      toast.error("Failed to update note", {
        description: error.message,
        ...failed,
      });
    },
  });
};

// Progress Stage Hook
export const useProgressStage = (applicationId: string) => {
  return useMutation({
    mutationFn: async (data: { currentStep: string }) => {
      const response = await fetch(
        `/api/applications/${applicationId}/current-step`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Stage progressed successfully", {
        description: "The application has been moved to the next stage",
        ...success,
      });
      revalidateTag(["applications", `application-${applicationId}`]);
    },
    onError: (error: Error) => {
      toast.error("Failed to progress stage", {
        description: error.message,
        ...failed,
      });
    },
  });
};

// Legacy hooks for backward compatibility
export const useCreateImmigrationDocument = useCreateDocumentMaster;
export const useUpdateImmigrationDocument = useUpdateDocumentMaster;

export const useDeleteImmigrationDocument = useDeleteDocumentMaster;

// ....................Profile.....................

export const useProfile = () => {
  const { data: session } = useSession();

  return useQuery({
    queryKey: ["profile", session?.user?.email],
    queryFn: async () => {
      const response = await fetch("/api/auth/profile");
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          return result;
        }
      }
      throw new Error("Failed to fetch profile");
    },
    enabled: !!session?.user, // Enable if we have any session
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create New Application Hooks
export const useCreateApplication = () => {
  return useMutation({
    mutationFn: async (data: ICreateApplicationRequest) => {
      const response = await fetch("/api/applications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create application");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Application created successfully", {
        description:
          "The application has been created and is ready for processing",
        ...success,
      });
      revalidateTag(["applications"]);
    },
    onError: (error: any) => {
      toast.error("Failed to create application", {
        description: error.message || "An unexpected error occurred",
        ...failed,
      });
    },
  });
};

export const useCreateUserForApplication = () => {
  return useMutation({
    mutationFn: async (data: ICreateUser) => {
      const response = await fetch("/api/user/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.name,
          email: data.email,
          emailVerified: true, // Set to true for password-less registration
          mobileNo: data.phone, // Map phone to mobileNo for the registration API
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to register user");
      }

      const result = await response.json();

      // Ensure we return the user data in a consistent format
      return {
        success: result.success || true,
        message: result.message || "User registered successfully",
        user: result.data || result.user || result,
        userId: result.data?.id || result.user?.id || result.id,
      };
    },
    onSuccess: (data: any) => {
      toast.success("User registered successfully", {
        description:
          "The user has been created and can now be used in applications",
        ...success,
      });
    },
    onError: (error: any) => {
      toast.error("Failed to register user", {
        description: error.message || "An unexpected error occurred",
        ...failed,
      });
    },
  });
};

export const useCreateImmigrationProductForApplication = () => {
  return useMutation({
    mutationFn: async (data: ICreateImmigrationProduct) => {
      const response = await fetch("/api/immigration", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to create immigration product"
        );
      }

      return response.json();
    },
    onError: (error: any) => {
      toast.error("Failed to create immigration product", {
        description: error.message || "An unexpected error occurred",
        ...failed,
      });
    },
  });
};
