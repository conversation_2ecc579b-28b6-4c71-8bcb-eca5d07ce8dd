"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUpdateNotificationSettings } from "@/hooks/notifications/use-query";

interface NotificationSettingsProps {
  settings: INotificationSetting[];
  onSettingsUpdate: () => void;
}

// Notification types with descriptions
const NOTIFICATION_TYPES = [
  {
    type: "document_reminder",
    label: "Document Reminder",
    description: "Reminders for pending document uploads",
    hasIntervals: true,
  },
  {
    type: "application_submitted",
    label: "Application Submitted",
    description: "Confirmation when application is submitted",
    hasIntervals: false,
  },
  {
    type: "agent_assigned",
    label: "Agent Assigned",
    description: "Notification when agent is assigned to application",
    hasIntervals: false,
  },
  {
    type: "status_update",
    label: "Status Update",
    description: "Updates on application status changes",
    hasIntervals: false,
  },
  {
    type: "agent_query",
    label: "Agent Query",
    description: "Notifications for agent queries and responses",
    hasIntervals: false,
  },
  {
    type: "document_rejected",
    label: "Document Rejected",
    description: "Alerts when documents are rejected",
    hasIntervals: false,
  },
  {
    type: "authority_query",
    label: "Authority Query",
    description: "Notifications for authority queries",
    hasIntervals: false,
  },
  {
    type: "deadline_warning",
    label: "Deadline Warning",
    description: "Warnings for approaching deadlines",
    hasIntervals: true,
  },
  {
    type: "missing_document",
    label: "Missing Document",
    description: "Alerts for missing required documents",
    hasIntervals: true,
  },
  {
    type: "eligibility_confirmation",
    label: "Eligibility Confirmation",
    description: "Service eligibility confirmations",
    hasIntervals: false,
  },
  {
    type: "payment_confirmation",
    label: "Payment Confirmation",
    description: "Payment processing confirmations",
    hasIntervals: false,
  },
  {
    type: "final_decision",
    label: "Final Decision",
    description: "Final application decision notifications",
    hasIntervals: false,
  },
  {
    type: "system_maintenance",
    label: "System Maintenance",
    description: "System maintenance and downtime alerts",
    hasIntervals: false,
  },
  {
    type: "escalation_notice",
    label: "Escalation Notice",
    description: "Internal escalation and reassignment notifications",
    hasIntervals: false,
  },
];

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  settings,
  onSettingsUpdate,
}) => {
  const [localSettings, setLocalSettings] = useState<
    Record<string, INotificationSetting>
  >({});
  const [hasChanges, setHasChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const updateMutation = useUpdateNotificationSettings();

  // Memoize settings map creation to prevent unnecessary recalculations
  const settingsMap = useMemo(() => {
    const map: Record<string, INotificationSetting> = {};

    // Create default settings for all notification types
    NOTIFICATION_TYPES.forEach((notifType) => {
      const existingSetting = settings.find(
        (s) => s.notificationType === notifType.type
      );
      map[notifType.type] = existingSetting || {
        userType: "admin",
        notificationType: notifType.type as NotificationType,
        channels: ["email"], // Default to email only
        isEnabled: true,
        customSchedule: notifType.hasIntervals ? 24 : undefined, // Default to 24 hours for interval types
      };
    });

    return map;
  }, [settings]);

  // Initialize local settings from props with change detection
  useEffect(() => {
    // Only update if settings actually changed
    const currentSettingsString = JSON.stringify(localSettings);
    const newSettingsString = JSON.stringify(settingsMap);

    if (currentSettingsString !== newSettingsString) {
      setLocalSettings(settingsMap);
      setHasChanges(false); // Reset changes when new data arrives
      setValidationErrors({}); // Clear validation errors
    }
  }, [settingsMap, localSettings]);

  // Validate custom schedule values
  const validateCustomSchedule = useCallback((value: number, notificationType: string): string | null => {
    if (value < 1) return "Schedule must be at least 1 hour";
    if (value > 8760) return "Schedule cannot exceed 8760 hours (1 year)";
    if (!Number.isInteger(value)) return "Schedule must be a whole number";
    return null;
  }, []);

  const handleSettingChange = useCallback((
    notificationType: string,
    field: keyof INotificationSetting,
    value: any
  ) => {
    // Clear validation error for this field
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`${notificationType}_${field}`];
      return newErrors;
    });

    // Validate custom schedule if applicable
    if (field === 'customSchedule' && typeof value === 'number') {
      const error = validateCustomSchedule(value, notificationType);
      if (error) {
        setValidationErrors(prev => ({
          ...prev,
          [`${notificationType}_${field}`]: error
        }));
        return; // Don't update if validation fails
      }
    }

    setLocalSettings((prev) => ({
      ...prev,
      [notificationType]: {
        ...prev[notificationType],
        [field]: value,
      },
    }));
    setHasChanges(true);
  }, [validateCustomSchedule]);

  const handleSave = useCallback(async () => {
    // Check for validation errors
    const hasErrors = Object.keys(validationErrors).length > 0;
    if (hasErrors) {
      return;
    }

    try {
      const settingsArray = Object.values(localSettings);
      await updateMutation.mutateAsync(settingsArray);
      setHasChanges(false);
      setValidationErrors({});
      onSettingsUpdate();
    } catch (error) {
      console.error("Failed to update notification settings:", error);
    }
  }, [localSettings, validationErrors, updateMutation, onSettingsUpdate]);

  const handleReset = useCallback(() => {
    setLocalSettings(settingsMap);
    setHasChanges(false);
    setValidationErrors({});
  }, [settingsMap]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Notification Preferences</h3>
          <p className="text-sm text-muted-foreground">
            Configure how and when you receive notifications
          </p>
        </div>
        <div className="flex gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleReset}>
              Reset
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || updateMutation.isPending || Object.keys(validationErrors).length > 0}
          >
            {updateMutation.isPending ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {NOTIFICATION_TYPES.map((notifType) => {
          const setting = localSettings[notifType.type];
          if (!setting) return null;

          return (
            <Card key={notifType.type}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">
                      {notifType.label}
                    </CardTitle>
                    <CardDescription>{notifType.description}</CardDescription>
                  </div>
                  <Switch
                    checked={setting.isEnabled}
                    onCheckedChange={(checked) =>
                      handleSettingChange(notifType.type, "isEnabled", checked)
                    }
                  />
                </div>
              </CardHeader>

              {setting.isEnabled && (
                <CardContent className="pt-0">
                  <div className="space-y-4">
                    {/* Custom Schedule for applicable types */}
                    {notifType.hasIntervals && (
                      <div>
                        <Label className="text-sm font-medium">
                          Custom Schedule (hours)
                        </Label>
                        <Input
                          type="number"
                          min="1"
                          max="8760"
                          value={setting.customSchedule || 24}
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || 24;
                            handleSettingChange(
                              notifType.type,
                              "customSchedule",
                              value
                            );
                          }}
                          className={`w-32 mt-2 ${
                            validationErrors[`${notifType.type}_customSchedule`]
                              ? "border-destructive"
                              : ""
                          }`}
                          placeholder="24"
                        />
                        {validationErrors[`${notifType.type}_customSchedule`] && (
                          <p className="text-xs text-destructive mt-1">
                            {validationErrors[`${notifType.type}_customSchedule`]}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground mt-1">
                          Reminder will be sent after this many hours from the
                          initial trigger
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>
    </div>
  );
};
