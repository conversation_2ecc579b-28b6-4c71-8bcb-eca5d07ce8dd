import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";

export const getMentors = async () => {
  const res = await fetch(`${apiUrl}/mentor?page=0&limit=0`, {
    next: {
      tags: ["mentors"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IMentor[];
  }

  return [];
};
export const getMentorById = async (id: string) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/mentor/admin/${id}`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["mentor", id],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IMentorInfo;
  }

  return null;
};

// ...........................ContactUs........................................

export const getContactUs = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/contact-us?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["contact-us"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IContactUs[];
  }

  return [];
};

// ........................ Blog....................................
export const getBlogs = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/blog?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["blogs"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IBlog[];
  }

  return [];
};
export const getBlog = async (slug: string) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/blog/${slug}`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["blog", slug],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IBlog;
  }

  return null;
};
// ........................ Customer Review....................................
export const getCustomerReviews = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/customer-review?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["customer-review"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as ICustomerReview[];
  }

  return [];
};
export const getCustomerReview = async (id: string) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/customer-review/${id}`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["customer-review", id],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as ICustomerReview;
  }

  return null;
};

// ...................... User .............................

export const getUsers = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/user/admin?page=0&limit=0`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["users"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IBlog[];
  }

  return [];
};
export const getUser = async (id: string) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/user/admin/${id}`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["user", id],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IUser;
  }

  return null;
};

// ............Packages.......................

export const getPackages = async () => {
  const res = await fetch(`${apiUrl}/packages`, {
    next: {
      tags: ["packages"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IPackage[];
  }

  return [];
};

// ............Immigration.......................
export const getImmigrations = async () => {
  const res = await fetch(`${apiUrl}/immigration`, {
    next: {
      tags: ["immigrations"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IImmigration[];
  }

  return [];
};

export async function getImmigrationById(id: string) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/immigration/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["immigrations", `immigration-${id}`],
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch immigration service");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching immigration service:", error);
    return null;
  }
}
// ............Training.......................
export const getTrainings = async () => {
  const res = await fetch(`${apiUrl}/training`, {
    next: {
      tags: ["trainings"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as ITraining[];
  }

  return [];
};

// ............Dashboard.......................

export const getDashboard = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const res = await fetch(`${apiUrl}/dashboard`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["dashboard"],
    },
    cache: "no-store",
  });
  const data = await res.json();

  if (res.status === 200) {
    return data as IDashboard;
  }

  return null;
};

// ................. Guest.........................
export const getGuestPurchaseData = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  const [
    servicesResponse,
    packagesResponse,
    immigrationResponse,
    trainingResponse,
  ] = await Promise.all([
    fetch(`${apiUrl}/guest/purchase/service?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-services"],
      },
      cache: "no-store",
    }),
    fetch(`${apiUrl}/guest/purchase/package?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-packages"],
      },
      cache: "no-store",
    }),
    fetch(`${apiUrl}/guest/purchase/immigration?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-immigration"],
      },
      cache: "no-store",
    }),
    fetch(`${apiUrl}/guest/purchase/training?page=0&limit=0`, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["guest-training"],
      },
      cache: "no-store",
    }),
  ]);

  const [servicesData, packagesData, immigrationData, trainingData] =
    await Promise.all([
      servicesResponse.json(),
      packagesResponse.json(),
      immigrationResponse.json(),
      trainingResponse.json(),
    ]);

  return {
    services:
      servicesResponse.status === 200 ? (servicesData as IGuestService[]) : [],
    packages:
      packagesResponse.status === 200 ? (packagesData as IGuestPackage[]) : [],
    immigration:
      immigrationResponse.status === 200
        ? (immigrationData as IGuestImmigration[])
        : [],
    trainings:
      trainingResponse.status === 200 ? (trainingData as IGuestTraining[]) : [],
  };
};

// ................. Purchases (v2 Payment API).........................
export const getPaymentHistory = async (
  page: number = 1,
  limit: number = 10,
  filters?: {
    serviceType?: string;
    paymentType?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }
) => {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  // Build query parameters
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (filters) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.append(key, value);
      }
    });
  }

  const res = await fetch(`${apiUrl}/v2/payment/history?${params.toString()}`, {
    headers: {
      Authorization: `Bearer ${session.backendTokens.accessToken}`,
    },
    next: {
      tags: ["payment-history"],
    },
    cache: "no-store",
  });

  const data = await res.json();

  if (res.status === 200) {
    return data as PaymentHistoryResponse;
  }

  return {
    page: 1,
    limit: 10,
    totalItems: 0,
    totalPages: 0,
    data: [],
  } as PaymentHistoryResponse;
};

// Document Master Server Functions
export async function getDocumentMasters(
  page: number = 1,
  limit: number = 10,
  search?: string,
  category?: string
) {
  const session = await getServerSession(authOptions);

  if (!session?.backendTokens?.accessToken) {
    redirect("/signin");
  }

  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(category && { category }),
    });

    const response = await fetch(`${apiUrl}/document-master?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: { tags: ["document-masters", "immigration-documents"] },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch document masters");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching document masters:", error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

// Legacy function for backward compatibility
export const getImmigrationDocuments = getDocumentMasters;

export async function getDocumentMaster(id: string) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/document-master/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: [
          "document-masters",
          "immigration-documents",
          `document-master-${id}`,
        ],
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch document master");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching document master:", error);
    return null;
  }
}

// Legacy function for backward compatibility
export const getImmigrationDocument = getDocumentMaster;

export async function getDocumentTypes() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/admin/document-types`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: { tags: ["document-types"] },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch document types");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching document types:", error);
    return { data: [] };
  }
}

// Workflow Master Server Functions
export async function getWorkflowMasters(
  page: number = 1,
  limit: number = 10,
  search?: string,
  isActive?: boolean,
  sortBy?: string,
  sortOrder?: string
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(isActive !== undefined && { is_active: isActive.toString() }),
      ...(sortBy && { sort_by: sortBy }),
      ...(sortOrder && { sort_order: sortOrder }),
    });

    const response = await fetch(`${apiUrl}/workflow-master?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: { tags: ["workflow-masters"] },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch workflow masters");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching workflow masters:", error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

export async function getWorkflowMaster(id: string) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/workflow-master/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["workflow-masters", `workflow-master-${id}`],
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch workflow master");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching workflow master:", error);
    return null;
  }
}

// Workflow Template Server Functions
export async function getWorkflowTemplates(
  page: number = 1,
  limit: number = 10,
  search?: string,
  serviceType?: string,
  isActive?: boolean,
  sortBy?: string,
  sortOrder?: string
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(serviceType && { serviceType }),
      ...(isActive !== undefined && { isActive: isActive.toString() }),
      ...(sortBy && { sort_by: sortBy }),
      ...(sortOrder && { sort_order: sortOrder }),
    });

    const response = await fetch(
      `${apiUrl}/workflow-templates?${queryParams}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
        },
        next: { tags: ["workflow-templates"] },
      }
    );

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch workflow templates");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching workflow templates:", error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

export async function getWorkflowTemplate(id: string) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/workflow-templates/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["workflow-templates", `workflow-template-${id}`],
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch workflow template");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching workflow template:", error);
    return null;
  }
}

// Applications Server Functions
export async function getApplications(
  page: number = 1,
  limit: number = 10,
  search?: string,
  serviceType?: string,
  status?: string,
  priorityLevel?: string
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(serviceType && { service_type: serviceType }),
      ...(status && { status }),
      ...(priorityLevel && { priority_level: priorityLevel }),
    });

    const response = await fetch(`${apiUrl}/applications?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: { tags: ["applications"] },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch applications");
    }

    const data = await response.json();
    return {
      data: data.data || [],
      pagination: {
        page: data.page || 1,
        limit: data.limit || 10,
        total: data.total || 0,
        totalPages: data.totalPages || 0,
      },
    };
  } catch (error) {
    console.error("Error fetching applications:", error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

// Application Management
export async function getApplicationById(id: string) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/applications/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["applications", `application-${id}`],
      },
      cache: "no-store",
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch application details");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching application details:", error);
    return null;
  }
}

// Agent Server Functions
export async function getAgents(
  page: number = 1,
  limit: number = 10,
  search?: string,
  status?: string,
  sortBy?: string,
  sortOrder?: string
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...(search && { search }),
      ...(status && { status }),
      ...(sortBy && { sort_by: sortBy }),
      ...(sortOrder && { sort_order: sortOrder }),
    });

    const response = await fetch(`${apiUrl}/agents?${queryParams}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: { tags: ["agents"] },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch agents");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching agents:", error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
    };
  }
}

export async function getAgent(id: string) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/signin");
  }

  try {
    const response = await fetch(`${apiUrl}/agents/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
      },
      next: {
        tags: ["agents", `agent-${id}`],
      },
    });

    if (!response.ok) {
      if (response.status === 401) {
        redirect("/signin");
      }
      throw new Error("Failed to fetch agent");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching agent:", error);
    return null;
  }
}
