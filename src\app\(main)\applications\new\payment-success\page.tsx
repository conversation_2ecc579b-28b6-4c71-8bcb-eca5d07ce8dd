"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>cle, XCircle, Loader2 } from "lucide-react";
import { useCreateApplication } from "@/hooks/use-query";

/**
 * Payment Success Page
 *
 * NOTE: As of v2.0.0 (2025-07-08), this page is primarily for legacy support.
 * The new workflow auto-completes Stripe payments without redirecting here.
 * This page may still be used for:
 * - External Stripe redirect URLs (if configured)
 * - Fallback scenarios where auto-completion fails
 * - Manual payment completion workflows
 *
 * Consider removing this page in future versions if no longer needed.
 *
 * @return {React.FC} Payment success page component
 */
const PaymentSuccessPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [message, setMessage] = useState("");

  const createApplicationMutation = useCreateApplication();

  useEffect(() => {
    const handlePaymentSuccess = async () => {
      try {
        // Get payment status from URL params
        const paymentStatus = searchParams.get("payment_status");
        const sessionId = searchParams.get("session_id");

        if (paymentStatus !== "success" || !sessionId) {
          setStatus("error");
          setMessage("Payment was not completed successfully");
          return;
        }

        // Get stored payment and application data
        const pendingPaymentData = sessionStorage.getItem("pendingPayment");
        const pendingApplicationData =
          sessionStorage.getItem("pendingApplication");

        if (!pendingPaymentData || !pendingApplicationData) {
          setStatus("error");
          setMessage(
            "Application data not found. Please start the application process again."
          );
          return;
        }

        const paymentData = JSON.parse(pendingPaymentData);
        const applicationData = JSON.parse(pendingApplicationData);

        // Create the application with the payment ID
        const payload = {
          ...applicationData,
          payments: [paymentData.paymentId],
        };

        await createApplicationMutation.mutateAsync(payload);

        // Clear stored data
        sessionStorage.removeItem("pendingPayment");
        sessionStorage.removeItem("pendingApplication");

        setStatus("success");
        setMessage("Payment successful! Your application has been created.");

        // Redirect to applications list after a delay
        setTimeout(() => {
          router.push("/applications");
        }, 3000);
      } catch (error) {
        console.error("Error completing application:", error);
        setStatus("error");
        setMessage("Failed to complete application. Please contact support.");
      }
    };

    handlePaymentSuccess();
  }, [searchParams, createApplicationMutation, router]);

  const handleReturnToApplications = () => {
    router.push("/applications");
  };

  const handleStartNewApplication = () => {
    // Clear any stored data
    sessionStorage.removeItem("pendingPayment");
    sessionStorage.removeItem("pendingApplication");
    router.push("/applications/new");
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            {status === "loading" && (
              <Loader2 className="h-6 w-6 animate-spin" />
            )}
            {status === "success" && (
              <CheckCircle className="h-6 w-6 text-green-500" />
            )}
            {status === "error" && <XCircle className="h-6 w-6 text-red-500" />}
            {status === "loading" && "Processing Payment..."}
            {status === "success" && "Payment Successful!"}
            {status === "error" && "Payment Failed"}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">{message}</p>

          {status === "success" && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                Redirecting to applications in 3 seconds...
              </p>
              <Button onClick={handleReturnToApplications} className="w-full">
                Go to Applications Now
              </Button>
            </div>
          )}

          {status === "error" && (
            <div className="space-y-2">
              <Button onClick={handleStartNewApplication} className="w-full">
                Start New Application
              </Button>
              <Button
                variant="outline"
                onClick={handleReturnToApplications}
                className="w-full"
              >
                Return to Applications
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentSuccessPage;
