import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * POST /api/applications/assign-workflow-template
 *
 * Assigns a new workflow template to an existing application.
 * This endpoint is specifically designed for the EditableWorkflowTemplateCell component
 * to update workflow templates with proper validation and error handling.
 *
 * @param {NextRequest} request - The incoming request containing application_id and new_workflow_template_id
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      application_id: applicationId,
      new_workflow_template_id: newWorkflowTemplateId,
    } = body;

    // Validate required fields
    if (!applicationId) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    if (!newWorkflowTemplateId) {
      return NextResponse.json(
        { success: false, message: "New workflow template ID is required" },
        { status: 400 }
      );
    }

    // Validate that IDs are strings and not empty
    if (typeof applicationId !== "string" || applicationId.trim() === "") {
      return NextResponse.json(
        { success: false, message: "Invalid application ID format" },
        { status: 400 }
      );
    }

    if (
      typeof newWorkflowTemplateId !== "string" ||
      newWorkflowTemplateId.trim() === ""
    ) {
      return NextResponse.json(
        { success: false, message: "Invalid workflow template ID format" },
        { status: 400 }
      );
    }

    // Assign workflow template in backend using the new API endpoint
    // This endpoint specifically handles workflow template assignment with proper validation
    const backendUrl = `${apiUrl}/applications/assign-workflow-template`;

    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        application_id: applicationId.trim(),
        new_workflow_template_id: newWorkflowTemplateId.trim(),
      }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Workflow template assigned successfully",
        data,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to assign workflow template",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error assigning workflow template:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
