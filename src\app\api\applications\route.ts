import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";
import { authenticatedFetch, handleApiResponse } from "@/lib/api-utils";
import { createApplicationApiSchema } from "@/utils/schema";
import { ZodError } from "zod";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get search parameters from the request URL
    const { searchParams } = new URL(request.url);

    // Forward all query parameters to the backend
    const backendUrl = `${apiUrl}/applications?${searchParams.toString()}`;

    const { response, data } = await authenticatedFetch(
      backendUrl,
      {},
      session.backendTokens.accessToken
    );

    return handleApiResponse(response, data, "Failed to fetch applications");
  } catch (error) {
    console.error("Error fetching applications:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate request payload
    try {
      const validatedData = createApplicationApiSchema.parse(body);

      // Create application in backend with validated data
      const backendUrl = `${apiUrl}/applications`;

      const response = await fetch(backendUrl, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(validatedData),
      });

      const data = await response.json();

      if (response.ok) {
        return NextResponse.json(data);
      } else {
        return NextResponse.json(
          {
            success: false,
            message: data.message || "Failed to create application",
          },
          { status: response.status }
        );
      }
    } catch (validationError) {
      if (validationError instanceof ZodError) {
        return NextResponse.json(
          {
            success: false,
            message: "Validation failed",
            errors: validationError.errors.map((err) => ({
              field: err.path.join("."),
              message: err.message,
            })),
          },
          { status: 400 }
        );
      }
      throw validationError;
    }
  } catch (error) {
    console.error("Error creating application:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
