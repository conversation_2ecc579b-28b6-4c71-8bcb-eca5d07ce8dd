"use client";

import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import revalidateTag from "@/utils/revalidate-tag";

// Toast configurations
const success = {
  style: {
    background: "#10B981",
    color: "#FFFFFF",
    border: "none",
  },
};

const failed = {
  style: {
    background: "#EF4444",
    color: "#FFFFFF",
    border: "none",
  },
};
// Request deduplication map to prevent multiple concurrent requests
const activeRequests = new Map<string, Promise<any>>();

// Notification Settings Hooks with optimization
export const useUpdateNotificationSettings = () => {
  return useMutation({
    mutationFn: async (data: INotificationSetting[]) => {
      // Create a unique key for this request to prevent duplicates
      const requestKey = `update-settings-${JSON.stringify(data).slice(0, 100)}`;

      // Check if there's already an active request with the same data
      if (activeRequests.has(requestKey)) {
        return activeRequests.get(requestKey);
      }

      // Create the request promise
      const requestPromise = (async () => {
        try {
          // Add timeout to prevent hanging requests
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

          const response = await fetch("/api/notifications/settings", {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ settings: data }),
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            const error = await response.json();
            throw new Error(
              error.message || "Failed to update notification settings"
            );
          }

          return response.json();
        } finally {
          // Remove from active requests when done
          activeRequests.delete(requestKey);
        }
      })();

      // Store the promise to prevent duplicate requests
      activeRequests.set(requestKey, requestPromise);

      return requestPromise;
    },
    onSuccess: (data) => {
      toast.success("Notification settings updated successfully", {
        description: "Your notification preferences have been saved",
        ...success,
      });
      revalidateTag(["notification-settings"]);
    },
    onError: (error: any) => {
      // Handle different types of errors
      let errorMessage = "Failed to update notification settings";
      let errorDescription = "Please try again later";

      if (error.name === "AbortError") {
        errorMessage = "Request timed out";
        errorDescription =
          "The request took too long. Please check your connection and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage, {
        description: errorDescription,
        ...failed,
      });
    },
    // Add retry configuration
    retry: (failureCount, error) => {
      // Don't retry on client errors (4xx) or abort errors
      if (
        error.name === "AbortError" ||
        (error.status >= 400 && error.status < 500)
      ) {
        return false;
      }
      // Retry up to 2 times for server errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};
